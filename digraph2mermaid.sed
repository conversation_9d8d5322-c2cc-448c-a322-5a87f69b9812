# This script can transform a dotfile result from `terraform graph`
# into mermaid-flowchart that can be used in README.md code sections
# or pasted into https://mermaid.live

# NB! mac users need to install [gnu sed] from brew 

# usage:
#    terraform graph | sed -f digraph2mermaid.sed

# remove headers
/^digraph\s{/d
/^\s*compound\s=\s/d
/^\s*newrank\s=\s/d

# rewrite this header
s/subgraph.*{/graph LR/

# we don't care about these nodes
/^.*\[root\]\sprovider\[.*$/d
/^.*\[root\]\svar\..*$/d
/^.*\[root\]\soutput\..*$/d
/^.*\[root\]\slocal\..*$/d

# make names into node-id syntax
s/"\[root\]\s/$root:/g
s/\s(expand)//g
s/\s(close)//g

# cleanup label,box
s/"\s\[label\s=\s/[/g
s/,\sshape\s=\s"[^"]*"//g

# fix links -->
s/"*\s*->\s*/ --> /
s/"$//

# footer-cleanup
/^\s*}\s*$/d
