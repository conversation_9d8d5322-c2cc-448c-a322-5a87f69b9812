locals {

  snowpipe_data_folders = {
    adt3_assignment_request = {
      table_name    = "ADT3_ASSIGNMENT_REQUEST_V1_RAW"
      pipe_name     = "ADT3_ASSIGNMENT_REQUEST_SNOWPIPE"
      data_folder   = "mqtt.adt.ota.api.v3.input.operational.assignment.attempt.request"
      file_format   = "json_format"
      table_comment = "Raw table of ADT3 Assignments requests from kafka"
      pipe_comment  = "A pipe to ingest the incoming files."
    }
    adt3_assignment_omit = {
      table_name    = "ADT3_ASSIGNMENT_OMIT_REQUEST_V1_RAW"
      pipe_name     = "ADT3_ASSIGNMENT_OMIT_V1_SNOWPIPE"
      data_folder   = "mqtt.adt.ota.api.v3.input.operational.assignment.omit.request"
      file_format   = "json_format"
      table_comment = "Raw table of Command Assignment omit messages from kafka"
      pipe_comment  = "A pipe to ingest the incoming files."
    }
    adt2_assignment_attempt = {
      table_name    = "ADT2_ASSIGNMENT_ATTEMPT_V1_RAW"
      pipe_name     = "ADT2_ASSIGNMENT_REQUEST_SNOWPIPE"
      data_folder   = "mqtt.adt.ota.api.v2.input.di.assignment.attempt.block"
      file_format   = "json_format"
      table_comment = "Raw table of ADT2 Assignments attempts from kafka"
      pipe_comment  = "A pipe to ingest the incoming files."
    }
    adt1_signon = {
      table_name    = "ADT1_ASSIGNMENT_SIGNON_V1_RAW"
      pipe_name     = "ADT1_ASSIGNMENT_SIGNON_SNOWPIPE"
      data_folder   = "dc.event.itxpt.signon.raw"
      file_format   = "json_format"
      table_comment = "Raw table of ADT1 Assignments signon messages from kafka"
      pipe_comment  = "A pipe to ingest the incoming files."
    }
    adt1_signoff = {
      table_name    = "ADT1_ASSIGNMENT_SIGNOFF_V1_RAW"
      pipe_name     = "ADT1_ASSIGNMENT_SIGNOFF_SNOWPIPE"
      data_folder   = "dc.event.itxpt.signoff.raw"
      file_format   = "json_format"
      table_comment = "Raw table of ADT1 Assignments signoff messages from kafka"
      pipe_comment  = "A pipe to ingest the incoming files."
    }
    command_assignment_attempt = {
      table_name    = "COMMAND_ASSIGNMENT_ATTEMPT_V1_RAW"
      pipe_name     = "COMMAND_ASSIGNMENT_ATTEMPT_V1_SNOWPIPE"
      data_folder   = "command.vehicle.assignment.assignment-attempt.v1"
      file_format   = "json_format"
      table_comment = "Raw table of Command Assignment attempt messages from kafka"
      pipe_comment  = "A pipe to ingest the incoming files."
    }
    deviation = {
      table_name         = "DEVIATION_RAW"
      pipe_name          = "DEVIATION_SNOWPIPE"
      data_folder        = "topic.name.raw"
      file_format        = "json_format"
      table_comment      = "Raw table of deviations from kafka"
      pipe_comment       = "A pipe to ingest the incoming files."
      use_default_columns = false
      custom_columns = [
        {
          name = "OWNER_ID"
          type = "VARCHAR"
        },
        {
          name = "OPERATOR_ID"
          type = "VARCHAR"
        },
        {
          name = "AUTHORITY_ID"
          type = "VARCHAR"
        },
        {
          name = "QUAY_REFS"
          type = "VARCHAR"
        },
        {
          name = "STOPPOINT_REFS"
          type = "VARCHAR"
        },
        {
          name = "DATEDJOURNEYV2_REFS"
          type = "VARCHAR"
        },
        {
          name = "LINE_REFS"
          type = "VARCHAR"
        },
        {
          name    = "JSON_BLOB"
          type    = "VARCHAR"
          comment = "entire BIServiceDeviation"
        }
      ]
    }
    mitigation = {
      table_name         = "MITIGATION_RAW"
      pipe_name          = "MITIGATION_SNOWPIPE"
      data_folder        = "topic.name.raw"
      file_format        = "json_format"
      table_comment      = "Raw table of mitigations from kafka"
      pipe_comment       = "A pipe to ingest the incoming files."
      use_default_columns = false
      custom_columns = [
        {
          name = "OWNER_ID"
          type = "VARCHAR"
        },
        {
          name = "OPERATOR_ID"
          type = "VARCHAR"
        },
        {
          name = "AUTHORITY_ID"
          type = "VARCHAR"
        },
        {
          name = "QUAY_REFS"
          type = "VARCHAR"
        },
        {
          name = "STOPPOINT_REFS"
          type = "VARCHAR"
        },
        {
          name = "DATEDJOURNEYV2_REFS"
          type = "VARCHAR"
        },
        {
          name = "LINE_REFS"
          type = "VARCHAR"
        },
        {
          name    = "JSON_BLOB"
          type    = "VARCHAR"
          comment = "entire BiServiceMitigation"
        }
      ]
    }
  }

  aws_accounts = {
    test         = "************"
    stage        = "************"
    prod         = "************"
    team_cmp     = "************"
    ruterbastion = "************"
  }

  snowflake_accounts = {
    dev   = "fl65924"
    stage = "hp76112"
    prod  = "nn71758"
  }

  snowflake_tf_user = {
    dev   = "dev_runner_service"
    stage = "staging_runner_service"
    prod  = "prod_runner_service"
  }

  snowflake_region = {
    dev   = "eu-west-1"
    stage = "eu-west-1"
    prod  = "eu-west-1"
  }

  localuser_provider_role_fmt = "arn:aws:iam::%s:role/eks-%s-${var.team_vpc}-${var.team}-team"
  suggested_region = "eu-west-1"

  # USEFUL shortcut
  this = local.wrkspc_config[terraform.workspace]

  # -----------------------------------------------------------------------------------
  # Workspace spesific config here
  # -----------------------------------------------------------------------------------
  wrkspc_config = {
     dev = {
      region                    = local.suggested_region
      env                       = "dev"
      account_name              = "test"
      account_num               = local.aws_accounts.test
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.test, "dev")
      kube_namespace            = "tranop-dev"
      slack_channel             = "team-tran-alerts-test"
      slack_channel_assignment  = "assignment-test-alerts"

      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "FL65924"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-dev-eu-west-1-assignment-dev"
    }

    test = {
      region                    = local.suggested_region
      env                       = "test"
      account_name              = "test"
      account_num               = local.aws_accounts.test
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.test, "dev")
      kube_namespace            = "tranop"
      slack_channel             = "team-tran-alerts-test"
      slack_channel_assignment  = "assignment-test-alerts"

      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "FL65924"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-dev-eu-west-1-assignment"
    }

    stage = {
      region                    = local.suggested_region
      env                       = "stage"
      account_name              = "stage"
      account_num               = local.aws_accounts.stage
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.stage, "stage")
      kube_namespace            = "tranop"
      slack_channel             = "team-tran-alerts-stage"
      slack_channel_assignment  = "assignment-stage-alerts"

      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "HP76112"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-stage-eu-west-1-assignment"
    }

    prod = {
      region                    = local.suggested_region
      env                       = "prod"
      account_name              = "prod"
      account_num               = local.aws_accounts.prod
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.prod, "prod")
      
      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "nn71758"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-prod-eu-west-1-assignment"
    }
  }

}

