resource "snowflake_table" "raw_table" {
  database        = var.database
  schema          = var.schema_name
  name            = var.table_name
  comment         = var.table_comment
  change_tracking = var.change_tracking

  # default columns, when use_default_columns is true
  dynamic "column" {
    for_each = var.use_default_columns ? [1] : []
    content {
      name = "TIMESTAMP"
      type = "TIMESTAMP_TZ(9)"
    }
  }

  dynamic "column" {
    for_each = var.use_default_columns ? [1] : []
    content {
      name     = "DATA"
      type     = "VARIANT"
      nullable = true
    }
  }

  dynamic "column" {
    for_each = var.use_default_columns ? [1] : []
    content {
      name     = "FILENAME"
      type     = "VARCHAR"
      nullable = true
    }
  }

  dynamic "column" {
    for_each = var.use_default_columns ? [1] : []
    content {
      name     = "FILE_ROW_NUMBER"
      type     = "NUMBER"
      nullable = true
    }
  }

  dynamic "column" {
    for_each = var.use_default_columns ? [1] : []
    content {
      name     = "UID"
      type     = "VARCHAR"
      nullable = true
    }
  }

  # custom columns, when use_default_columns is false
  dynamic "column" {
    for_each = var.custom_columns != null ? var.custom_columns : []
    content {
      name     = column.value.name
      type     = column.value.type
      nullable = column.value.nullable
      comment  = column.value.comment
    }
  }
}

resource "snowflake_pipe" "snowpipe" {
  database = var.database
  schema   = var.schema_name
  name     = var.pipe_name
  comment  = var.pipe_comment
  copy_statement = var.custom_copy_statement != null ? var.custom_copy_statement : templatefile("${path.module}/copy_statement.sql", {
    database_name = var.database
    schema_name   = var.schema_name
    table_name    = snowflake_table.raw_table.name
    stage_name    = var.stage_name
    data_folder   = var.data_folder
    file_format   = var.file_format
  })
  auto_ingest = true
}

resource "snowflake_grant_privileges_to_account_role" "snowpipe_grant" {
  for_each          = toset(var.developer_roles)
  privileges        = ["OPERATE"]
  account_role_name = each.value
  on_schema_object {
    object_type = "PIPE"
    object_name = "${var.database}.${var.schema_name}.${snowflake_pipe.snowpipe.name}"
  }
  depends_on = [snowflake_pipe.snowpipe]
}
