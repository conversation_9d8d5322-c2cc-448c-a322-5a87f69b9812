# backend.tf , args MUST be hardcoded
# do not change

terraform {
  backend "s3" {
    key = "assignment-snowflake_s3_loading/terraform.tfstate"
    region         = "eu-west-1"                                                                    # fixed
    role_arn       = "arn:aws:iam::028715740751:role/tranop-iac-backend-access" # fixed
    bucket         = "tranop-iac-backend-ruterbastion"                          # fixed
    dynamodb_table = "tranop-iac-lock"                                          # fixed
    session_name   = "tranop-admin"                                             # optional
  }

  required_providers {
    snowflake = {
      source  = "Snowflake-Labs/snowflake"
      version = "0.70.1"
    }
  }
}
