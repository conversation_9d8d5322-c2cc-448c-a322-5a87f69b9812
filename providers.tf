
provider "aws" {
  region = local.this.region 

  dynamic "assume_role" {
    for_each = var.is_cicd ? [] : [1]
    content {
      role_arn = format(local.this.localuser_assume_role_arn)
    }
  }

}



#getting the key for the team_specific service account to snowflake
data "aws_ssm_parameter" "snowflake_service_user_private_key" {
  name = "/core-data-platform/share/tranop/snowflake_assignment_su_key"
}

provider "snowflake" {
  role                   = "ASSIGNMENT_TRANSFORMER"
  region                 = local.this.snowflake_region
  username               = local.this.snowflake_username
  account                = local.this.snowflake_account
  private_key            = data.aws_ssm_parameter.snowflake_service_user_private_key.value
}

