# -------------------------------------------------------------
#  
#   NOTE: variables are simply CONSTANTS,
#         shared across workspaces
#
#   use config.tf to give workspace SPECIFIC locals
# -------------------------------------------------------------

variable "is_cicd" {
  description = "Provider; Make sure to assume a role when local/workstation"
  default     = false
}

variable "team" {
  # set in shell by cicd-master
  description = "Team name"
  default     = "tranop"
}

variable "project_name" {
  # set in shell by cicd-master
  description = "Gitlab project name"
  default     = "**********************"
}

variable "team_vpc" {
  # Set in shell by cicd-master
  # Also set in input-*.tfvars for use in resources
  description = "Name of team VPC (pd or gp). Note! Need to be mapped to correct VPC name in AWS."
  default     = "gp"
}
