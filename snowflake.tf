resource "snowflake_schema" "raw_schema" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_RAW" : "RAW"
}

resource "snowflake_schema" "public" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_SHARED" : "SHARED"
}

resource "snowflake_schema" "data_products" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_DATA_PRODUCTS" : "DATA_PRODUCTS"
}

resource "snowflake_grant_privileges_to_account_role" "grant_data_products" {
  privileges        = ["USAGE"]
  account_role_name = "RUTER_PRODUCT_READER"
  on_schema {
    schema_name = "ASSIGNMENT.${snowflake_schema.data_products.name}"
  }
  depends_on = [snowflake_schema.data_products]
}

resource "snowflake_schema" "intermediate" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_INTERMEDIATE" : "INTERMEDIATE"
}

resource "snowflake_grant_privileges_to_account_role" "grant_intermediate" {
  privileges        = ["USAGE"]
  account_role_name = "RUTER_PRODUCT_READER"
  on_schema {
    schema_name = "ASSIGNMENT.${snowflake_schema.intermediate.name}"
  }
  depends_on = [snowflake_schema.intermediate]
}

resource "snowflake_grant_privileges_to_account_role" "grant_database_to_plandata_developer" {
  privileges        = ["USAGE"]
  account_role_name = "PLANDATA_DEVELOPER"
  on_account_object {
    object_type = "DATABASE"
    object_name = "ASSIGNMENT"
  }
}

resource "snowflake_grant_privileges_to_account_role" "grant_schema_plandata_developer" {
  privileges        = ["USAGE"]
  account_role_name = "PLANDATA_DEVELOPER"
  on_schema {
    schema_name = "ASSIGNMENT.${snowflake_schema.raw_schema.name}"
  }
  depends_on = [snowflake_schema.raw_schema]
}

resource "snowflake_grant_privileges_to_account_role" "grant_select_on_table_to_plandata_developer" {
  privileges        = ["SELECT"]
  account_role_name = "PLANDATA_DEVELOPER"
  on_schema_object {
    all {
      in_schema = "ASSIGNMENT.${snowflake_schema.raw_schema.name}"
      object_type_plural = "TABLES"
    }
  }
}


#Here is a stage for the example-folder in the snowflake_aws.tf file
resource "snowflake_stage" "assignment_stage" {
  name                = "ASSIGNMENT_STAGE"
  url                 = "s3://${aws_s3_bucket.snowflake_data_load_bucket.bucket}/${aws_s3_object.topic_folder.key}"
  database            = "ASSIGNMENT"
  schema              = snowflake_schema.raw_schema.name
  storage_integration = "CDP_S3_SHARED"
  depends_on     = [aws_s3_bucket.snowflake_data_load_bucket]
}

#Granting access to the example_stage to the {TEAM}_DEVELOPER role
resource "snowflake_grant_privileges_to_account_role" "assignment_stage" {
  privileges        = ["USAGE"]
  account_role_name = "ASSIGNMENT_DEVELOPER"
  on_schema_object {
    object_type = "STAGE"
    object_name = "ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}"
  }
}

resource "snowflake_file_format" "parquet_format" {
  name        = "PARQUET_FORMAT"
  database    = "ASSIGNMENT"
  schema      = snowflake_schema.raw_schema.name
  format_type = "PARQUET"
  binary_as_text = true
}

resource "snowflake_file_format" "json_format" {
  name        = "JSON_FORMAT"
  database    = "ASSIGNMENT"
  schema      = snowflake_schema.raw_schema.name
  format_type = "JSON"
  binary_as_text = true
}

resource "snowflake_table" "entity_assignment_v1_raw_current_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_ASSIGNMENT_V1_RAW_CURRENT_V1"
  comment  = "Raw table containing current version of AssignmentV1 from kafka"

  column {
    name = "TIMESTAMP"
    type = "TIMESTAMP_TZ(9)"
  }
   column {
    name = "DATA"
    type = "VARIANT"
  }
  column {
    name = "KEY"
    type = "VARCHAR(16777216)"
  }

  column {
    name = "PUBLISHED_TIMESTAMP"
    type = "TIMESTAMP_TZ(9)"
  }

  column {
    name = "DELETED"
    type = "TIMESTAMP_TZ(9)"
  }
}

resource "snowflake_table" "dated_journey_v2_events" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "DATED_JOURNEY_V2_JOURNEY_EVENT"
  comment  = "Table containing events on dated journey v2"

  column {
    name = "REF"
    type = "VARCHAR"
  }
  column {
    name = "TYPE"
    type = "VARCHAR"
  }
  column {
    name = "ASSIGNMENT_REF"
    type = "VARCHAR"
  }
  column {
    name = "DATED_JOURNEY_V2_REF"
    type = "VARCHAR"
  }
  column {
    name = "METADATA"
    type = "VARIANT"
  }
  column {
    name = "CREATED_AT"
    type = "TIMESTAMP_TZ(9)"
  }
}
