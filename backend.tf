# backend.tf , args MUST be hardcoded
# do not change

terraform {
  backend "s3" {
    key = "assignment-snowflake_s3_loading/terraform.tfstate"
    region         = "eu-west-1"                                                                    # fixed
    assume_role    = {
      role_arn     = "arn:aws:iam::028715740751:role/tranop-iac-backend-access" # fixed
      session_name = "tranop-admin"                                             # optional
    }
    use_lockfile   = true
    bucket         = "tranop-iac-backend-ruterbastion"                          # fixed
  }

  required_providers {
    snowflake = {
      source  = "Snowflake-Labs/snowflake"
      version = "0.95.0"
    }
  }
}
