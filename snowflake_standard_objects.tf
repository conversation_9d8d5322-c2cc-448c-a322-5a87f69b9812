module "snowpipe_objects" {
  source = "./modules/snowflake_object"

  for_each = local.snowpipe_data_folders

  database            = "ASSIGNMENT"
  schema_name         = snowflake_schema.raw_schema.name
  table_name          = each.value.table_name
  table_comment       = each.value.table_comment
  pipe_name           = each.value.pipe_name
  pipe_comment        = each.value.pipe_comment
  stage_name          = snowflake_stage.assignment_stage.name
  data_folder         = each.value.data_folder
  file_format         = each.value.file_format
  use_default_columns = lookup(each.value, "use_default_columns", true)
  custom_columns      = lookup(each.value, "custom_columns", null)

  depends_on = [
    snowflake_schema.raw_schema,
    snowflake_stage.assignment_stage,
    snowflake_file_format.json_format,
    snowflake_file_format.parquet_format
  ]
}
