output "table_name" {
  description = "The name of the created table"
  value       = snowflake_table.raw_table.name
}

output "table_id" {
  description = "The ID of the created table"
  value       = snowflake_table.raw_table.id
}

output "pipe_name" {
  description = "The name of the created pipe"
  value       = snowflake_pipe.snowpipe.name
}

output "pipe_id" {
  description = "The ID of the created pipe"
  value       = snowflake_pipe.snowpipe.id
}

output "pipe_notification_channel" {
  description = "The notification channel for the pipe (for S3 notifications)"
  value       = snowflake_pipe.snowpipe.notification_channel
}
