resource "snowflake_table" "raw_table" {
  database        = var.database
  schema          = var.schema_name
  name            = var.table_name
  comment         = var.table_comment
  change_tracking = var.change_tracking

  column {
    name = "TIMESTAMP"
    type = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }

  column {
    name     = "FILENAME"
    type     = "VARCHAR"
    nullable = true
  }

  column {
    name     = "FILE_ROW_NUMBER"
    type     = "NUMBER"
    nullable = true
  }

  column {
    name     = "UID"
    type     = "VARCHAR"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe" {
  database       = var.database
  schema         = var.schema_name
  name           = var.pipe_name
  comment        = var.pipe_comment
  copy_statement = templatefile("${path.module}/copy_statement.sql", {
    database_name = var.database
    schema_name   = var.schema_name
    table_name    = snowflake_table.raw_table.name
    stage_name    = var.stage_name
    data_folder   = var.data_folder
    file_format   = var.file_format
  })
  auto_ingest = true
}

resource "snowflake_pipe_grant" "snowpipe_grant" {
  database_name     = var.database
  schema_name       = var.schema_name
  pipe_name         = snowflake_pipe.snowpipe.name
  privilege         = "OPERATE"
  roles             = var.developer_roles
  on_future         = false
  with_grant_option = false
  depends_on        = [snowflake_pipe.snowpipe]
}
