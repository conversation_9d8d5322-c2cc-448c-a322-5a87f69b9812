resource "snowflake_schema" "raw_schema" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_RAW" : "RAW"
}

resource "snowflake_schema" "public" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_SHARED" : "SHARED"
}

resource "snowflake_schema" "data_products" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_DATA_PRODUCTS" : "DATA_PRODUCTS"
}

resource "snowflake_schema_grant" "grant_data_products" {
  database_name          = "ASSIGNMENT"
  schema_name            = snowflake_schema.data_products.name
  privilege              = "USAGE"
  roles                  = ["RUTER_PRODUCT_READER"]
  depends_on             = [snowflake_schema.data_products]
  enable_multiple_grants = true
}

resource "snowflake_schema" "intermediate" {
  database = "ASSIGNMENT"
  name = terraform.workspace == "dev" ? "DEV_INTERMEDIATE" : "INTERMEDIATE"
}

resource "snowflake_schema_grant" "grant_intermediate" {
  database_name          = "ASSIGNMENT"
  schema_name            = snowflake_schema.intermediate.name
  privilege              = "USAGE"
  roles                  = ["RUTER_PRODUCT_READER"]
  depends_on             = [snowflake_schema.intermediate]
  enable_multiple_grants = true
}

resource "snowflake_database_grant" "grant_database_to_plandata_developer" {
  database_name = "ASSIGNMENT"
  privilege     = "USAGE"
  roles         = ["PLANDATA_DEVELOPER"]
  enable_multiple_grants = true
}

resource "snowflake_schema_grant" "grant_schema_plandata_developer" {
  database_name          = "ASSIGNMENT"
  schema_name            = snowflake_schema.raw_schema.name
  privilege              = "USAGE"
  roles                  = ["PLANDATA_DEVELOPER"]
  depends_on             = [snowflake_schema.raw_schema]
  enable_multiple_grants = true
}

resource "snowflake_grant_privileges_to_role" "grant_select_on_table_to_plandata_developer" {
  privileges = ["SELECT"]
  role_name  = "PLANDATA_DEVELOPER"
  on_schema_object {
    all {
      in_schema = "ASSIGNMENT.${snowflake_schema.raw_schema.name}"
      object_type_plural = "TABLES"
    }
  }
}


#Here is a stage for the example-folder in the snowflake_aws.tf file
resource "snowflake_stage" "assignment_stage" {
  name                = "ASSIGNMENT_STAGE"
  url                 = "s3://${aws_s3_bucket.snowflake_data_load_bucket.bucket}/${aws_s3_object.topic_folder.key}"
  database            = "ASSIGNMENT"
  schema              = snowflake_schema.raw_schema.name
  storage_integration = "CDP_S3_SHARED"
  depends_on     = [aws_s3_bucket.snowflake_data_load_bucket]
}

#Granting access to the example_stage to the {TEAM}_DEVELOPER role
resource "snowflake_stage_grant" "assignment_stage" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  roles         = ["ASSIGNMENT_DEVELOPER"]
  privilege     = "USAGE"
  stage_name    = snowflake_stage.assignment_stage.name
}

resource "snowflake_file_format" "parquet_format" {
  name        = "PARQUET_FORMAT"
  database    = "ASSIGNMENT"
  schema      = snowflake_schema.raw_schema.name
  format_type = "PARQUET"
  binary_as_text = true
}

resource "snowflake_file_format" "json_format" {
  name        = "JSON_FORMAT"
  database    = "ASSIGNMENT"
  schema      = snowflake_schema.raw_schema.name
  format_type = "JSON"
  binary_as_text = true
}

# Entity assingment
resource "snowflake_table" "assignment_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_ASSIGNMENT_V1_RAW"
  comment  = "Raw table of Assingments from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_assignment" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ASSIGNMENT_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files to example folder."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.assignment_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/entity.assignment.key/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.parquet_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_assignment_grant" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_assignment.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_assignment]
}

# Entity assingment v2
resource "snowflake_table" "assignment_raw_v2" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_ASSIGNMENT_V1_RAW_V2"
  comment  = "Raw table of Assingments from kafka"
  change_tracking = true

  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "KEY"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATING_DAY"
    type     = "DATE"
  }

  column {
    name     = "VEHICLE_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATOR_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "PUBLISHED_TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }


}

resource "snowflake_pipe" "snowpipe_assignment_v2" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ASSIGNMENT_SNOWPIPE_v2"
  comment  = "A pipe to ingest the incoming files to example folder."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.assignment_raw_v2.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp())::TIMESTAMP_LTZ AS timestamp,
      $1:entityHeader:key::VARCHAR as key,
      coalesce($1:entityData:intention:operatingDay::DATE,
                $1:entityHeader:eventTimestamp::DATE) as operating_day,
      $1:entityData:vehicleRef::VARCHAR as vehicle_ref,
      $1:entityData:operatorRef::VARCHAR as operator_ref,
      $1:entityHeader:publishedTimestamp::TIMESTAMP_LTZ as published_timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/entity.assignment.key/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.parquet_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_assignment_grant_v2" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_assignment_v2.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_assignment]
}

# Entity vehicle_api v2
resource "snowflake_table" "vehicle_api_raw_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_VEHICLE_API_V1_RAW_V1"
  comment  = "Raw table of Vehicle API from kafka"
  change_tracking = true

  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "KEY"
    type     = "VARCHAR"
  }

  column {
    name     = "VEHICLE_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATOR_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "PUBLISHED_TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_vehicle_api_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "VEHICLE_API_SNOWPIPE_V1"
  comment  = "A pipe to ingest the incoming files to example folder."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.vehicle_api_raw_v1.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp())::TIMESTAMP_LTZ AS timestamp,
      $1:entityHeader:key::VARCHAR as key,
      $1:entityData:vehicleRef::VARCHAR as vehicle_ref,
      $1:entityData:apiOperatorRef::VARCHAR as operator_ref,
      $1:entityHeader:publishedTimestamp::TIMESTAMP_LTZ as published_timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/entity.vehicle.api.v1/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.parquet_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_vehicle_api_grant_v1" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_vehicle_api_v1.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_vehicle_api_v1]
}


resource "snowflake_table" "entity_assignment_v1_raw_current_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_ASSIGNMENT_V1_RAW_CURRENT_V1"
  comment  = "Raw table containing current version of AssignmentV1 from kafka"

  column {
    name = "TIMESTAMP"
    type = "TIMESTAMP_TZ(9)"
  }
   column {
    name = "DATA"
    type = "VARIANT"
  }
  column {
    name = "KEY"
    type = "VARCHAR(16777216)"
  }

  column {
    name = "PUBLISHED_TIMESTAMP"
    type = "TIMESTAMP_TZ(9)"
  }

  column {
    name = "DELETED"
    type = "TIMESTAMP_TZ(9)"
  }
}

resource "snowflake_table" "dated_journey_v2_events" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "DATED_JOURNEY_V2_JOURNEY_EVENT"
  comment  = "Table containing events on dated journey v2"

  column {
    name = "REF"
    type = "VARCHAR"
  }
  column {
    name = "TYPE"
    type = "VARCHAR"
  }
  column {
    name = "ASSIGNMENT_REF"
    type = "VARCHAR"
  }
  column {
    name = "DATED_JOURNEY_V2_REF"
    type = "VARCHAR"
  }
  column {
    name = "METADATA"
    type = "VARIANT"
  }
  column {
    name = "CREATED_AT"
    type = "TIMESTAMP_TZ(9)"
  }
}
