
# Deviation and Mitigation resources with custom column structures
# These resources have different column structures than the standard module



resource "snowflake_table" "deviation_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "DEVIATION_RAW"
  comment  = "Raw table of deviations from kafka"
  change_tracking = true
  column {
    name     = "OWNER_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATOR_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "AUTHORITY_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "QUAY_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "STOPPOINT_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "DATEDJOURNEYV2_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "LINE_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "JSON_BLOB"
    type     = "VARCHAR"
    comment  = "entire BIServiceDeviation"
  }
}

resource "snowflake_pipe" "snowpipe_deviation" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "DEVIATION_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.deviation_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      -- TODO where do we get this
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/topic.name.raw/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_grant_privileges_to_account_role" "snowpipe_devation" {
  privileges        = ["OPERATE"]
  account_role_name = "ASSIGNMENT_DEVELOPER"
  on_schema_object {
    object_type = "PIPE"
    object_name = "ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_pipe.snowpipe_deviation.name}"
  }
  depends_on = [snowflake_pipe.snowpipe_deviation]
}

resource "snowflake_table" "mitigation_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "MITIGATION_RAW"
  comment  = "Raw table of mitigations from kafka"
  change_tracking = true
  column {
    name     = "OWNER_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATOR_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "AUTHORITY_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "QUAY_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "STOPPOINT_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "DATEDJOURNEYV2_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "LINE_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "JSON_BLOB"
    type     = "VARCHAR"
    comment  = "entire BiServiceMitigation"
  }
}

resource "snowflake_pipe" "snowpipe_mitigation" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "MITIGATION_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.mitigation_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      -- TODO where do we get this
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/topic.name.raw/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_grant_privileges_to_account_role" "snowpipe_mitigation" {
  privileges        = ["OPERATE"]
  account_role_name = "ASSIGNMENT_DEVELOPER"
  on_schema_object {
    object_type = "PIPE"
    object_name = "ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_pipe.snowpipe_mitigation.name}"
  }
  depends_on = [snowflake_pipe.snowpipe_mitigation]
}
