#Create an encrypted bucket and restrict access from public
resource "aws_s3_bucket" "snowflake_data_load_bucket" {
  bucket = local.this.bucket
}

resource "aws_s3_bucket_public_access_block" "snowflake_data_load_bucket_access_block" {
  bucket = aws_s3_bucket.snowflake_data_load_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

#Creating an example folder to dump some files in
resource "aws_s3_object" "topic_folder" {
  bucket = aws_s3_bucket.snowflake_data_load_bucket.bucket
  key    = "topics"
}


#Notifications for standard module-generated pipes
resource "aws_s3_bucket_notification" "standard_snowpipe_notifications" {
  bucket = aws_s3_bucket.snowflake_data_load_bucket.bucket

  dynamic "queue" {
    for_each = local.snowpipe_data_folders
    content {
      id            = "snowpipe-${queue.key}"
      queue_arn     = module.snowpipe_objects[queue.key].pipe_notification_channel
      events        = ["s3:ObjectCreated:*"]
      filter_prefix = "topics/${queue.value.data_folder}/"
    }
  }

  depends_on = [module.snowpipe_objects]
}

