# ----------------------------------------------------
# - inputs
# external projects(IAC) resource IDs
# ----------------------------------------------------


locals {

  # usefull ssm-params for network bound resources
  # SYNC from https://github.com/basefarm/ruter-cloud-iac-v2/blob/master/prod/core/vpc/ssm-export.tf
  core_vpc_keys = ["vpc_cidr", "region", "vpc_id", "public_subnets",
    "private_subnets_extra", "database_subnets",
    "sg_vpce_s3_access_hosts_id", "priv_subnet_cidrs",
    "priv_subnet_extra_cidrs", "public_subnet_cidrs",
  "database_subnet_cidrs"]
}

# the collection of metadata values from above keys
data "aws_ssm_parameter" "core_vpc_value" {
  for_each = toset(local.core_vpc_keys)
  name     = "${local.ssmp_cmp_priv_prefix}core/vpc/${each.key}"
}

