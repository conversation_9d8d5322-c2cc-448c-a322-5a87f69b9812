

data "aws_caller_identity" "current" {}

locals {

  account_id = data.aws_caller_identity.current.account_id

  ssmp_cmp_priv_prefix = "/tf-state/cmp-private/${var.team_vpc}/" # used for input

  # Have to match IAM policy for team,
  #   https://gitlab.com/ruter-as/cmp/common/terraform-modules/teams-iam/-/blob/master/common_pols.tf
  bucket_prefix = "ruter-${var.team}"

  tags = {
   "CostCenter" = var.team
   "Terraform" = "true"
   "cicd" = "gitlab.com/ruter-as"
   # <add custom extra's>
  }

}
