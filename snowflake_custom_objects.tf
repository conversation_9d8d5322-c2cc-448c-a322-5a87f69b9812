resource "snowflake_table" "assignment_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_ASSIGNMENT_V1_RAW"
  comment  = "Raw table of Assignments from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_assignment" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ASSIGNMENT_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files to example folder."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.assignment_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/entity.assignment.key/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.parquet_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_assignment_grant" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_assignment.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_assignment]
}

# Entity assignment v2 (extended schema with field extraction)
resource "snowflake_table" "assignment_raw_v2" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_ASSIGNMENT_V1_RAW_V2"
  comment  = "Raw table of Assignments from kafka"
  change_tracking = true

  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "KEY"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATING_DAY"
    type     = "DATE"
  }

  column {
    name     = "VEHICLE_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATOR_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "PUBLISHED_TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_assignment_v2" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ASSIGNMENT_SNOWPIPE_v2"
  comment  = "A pipe to ingest the incoming files to example folder."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.assignment_raw_v2.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp())::TIMESTAMP_LTZ AS timestamp,
      $1:entityHeader:key::VARCHAR as key,
      coalesce($1:entityData:intention:operatingDay::DATE,
                $1:entityHeader:eventTimestamp::DATE) as operating_day,
      $1:entityData:vehicleRef::VARCHAR as vehicle_ref,
      $1:entityData:operatorRef::VARCHAR as operator_ref,
      $1:entityHeader:publishedTimestamp::TIMESTAMP_LTZ as published_timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/entity.assignment.key/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.parquet_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_assignment_grant_v2" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_assignment_v2.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_assignment]
}

# Entity vehicle API v1 (extended schema with field extraction)
resource "snowflake_table" "vehicle_api_raw_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ENTITY_VEHICLE_API_V1_RAW_V1"
  comment  = "Raw table of Vehicle API from kafka"
  change_tracking = true

  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "KEY"
    type     = "VARCHAR"
  }

  column {
    name     = "VEHICLE_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATOR_REF"
    type     = "VARCHAR"
  }

  column {
    name     = "PUBLISHED_TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_vehicle_api_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "VEHICLE_API_SNOWPIPE_V1"
  comment  = "A pipe to ingest the incoming files to example folder."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.vehicle_api_raw_v1.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp())::TIMESTAMP_LTZ AS timestamp,
      $1:entityHeader:key::VARCHAR as key,
      $1:entityData:vehicleRef::VARCHAR as vehicle_ref,
      $1:entityData:apiOperatorRef::VARCHAR as operator_ref,
      $1:entityHeader:publishedTimestamp::TIMESTAMP_LTZ as published_timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/entity.vehicle.api.v1/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.parquet_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_vehicle_api_grant_v1" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_vehicle_api_v1.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_vehicle_api_v1]
}
