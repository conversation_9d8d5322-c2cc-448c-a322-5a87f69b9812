variable "database" {
  description = "The Snowflake database name"
  type        = string
}

variable "schema_name" {
  description = "The Snowflake schema name"
  type        = string
}

variable "table_name" {
  description = "The name of the raw table"
  type        = string
}

variable "table_comment" {
  description = "Comment for the raw table"
  type        = string
}

variable "pipe_name" {
  description = "The name of the snowpipe"
  type        = string
}

variable "pipe_comment" {
  description = "Comment for the snowpipe"
  type        = string
}

variable "stage_name" {
  description = "The name of the stage to read from"
  type        = string
}

variable "data_folder" {
  description = "The folder path within the stage"
  type        = string
}

variable "file_format" {
  description = "The file format to use (json_format or parquet_format)"
  type        = string
}

variable "developer_roles" {
  description = "List of developer roles to grant pipe access to"
  type        = list(string)
  default     = ["ASSIGNMENT_DEVELOPER"]
}

variable "change_tracking" {
  description = "Enable change tracking on the table"
  type        = bool
  default     = true
}

variable "custom_columns" {
  description = "Custom columns, when the default fields aren't enough"
  type = list(object({
    name     = string
    type     = string
    nullable = optional(bool, true)
    comment  = optional(string, null)
  }))
  default = null
}

// TODO can maybe just check if custom_columns is set?
variable "use_default_columns" {
  description = "Whether to use the default columns. (TIMESTAMP, DATA, FILENAME etc) Set to false to use custom columns"
  type        = bool
  default     = true
}
