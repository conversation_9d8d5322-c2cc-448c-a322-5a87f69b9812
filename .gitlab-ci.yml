---
stages:
  - init
  - plan
  - apply

variables:
  TF_VAR_is_cicd: "true"
  # Below is SOMETIMES used in cmp-review branch
  TF_VAR_pack_reg_token_header: "JOB-TOKEN"
  TF_VAR_pack_reg_token: "${CI_JOB_TOKEN}"
  TF_VAR_pack_reg_ver: 2022

image:
  # This is premade to have [gnu-make, aws-cli, tfenv]
  # It SHOULD BE updated WHEN this template has a .terraform-version bump !
  name: 822152007605.dkr.ecr.eu-west-1.amazonaws.com/cmp/tf-utility:latest

# macros

.sshkey: &sshkey
  - echo "disabled out of the box, uncomment to get Read-Access into CMP group"
  # this FILE-variable must be added to project/group (and be owned by a team-user w access)
  # - chmod 600 $CMP_TF_MODULES_PRIV_KEY
  # - mkdir -p ~/.ssh
  # - mv $CMP_TF_MODULES_PRIV_KEY ~/.ssh/id_rsa
  # - ssh-keyscan -H 'gitlab.com' >> ~/.ssh/known_hosts

.plan_common: &plan_common
  stage: plan
  variables: {} # TF_WRK_SPC
  needs: ["init"]
  artifacts: {"paths": ["plan*.out",".terraform/",".terraform.lock.hcl"]}
  script:
    - terraform workspace select ${TF_WRK_SPC} || terraform workspace new ${TF_WRK_SPC}
    - terraform workspace show
    - terraform refresh > /dev/null # noise redux 
    - terraform plan -refresh=false -compact-warnings -out=plan_${TF_WRK_SPC}.out

.apply_common: &apply_common
  stage: apply
  variables: {} # TF_WRK_SPC
  script:
    - terraform workspace select ${TF_WRK_SPC}
    - terraform apply -compact-warnings plan_${TF_WRK_SPC}.out
  when: manual

# =========================================
#
#               -- jobs --
# 
# =========================================


#  --- I N I T once
init:
  script:
    - terraform init -upgrade
  stage: init
  tags: ["dev-gp-1"]
  artifacts: {"paths": [".terraform/",".terraform.lock.hcl"]}


# --- env:dev

"dev plan":
  <<: *plan_common
  variables: {"TF_WRK_SPC":"dev"}
  tags: ["dev-gp-1"] 

"dev apply":
  <<: *apply_common
  variables: {"TF_WRK_SPC":"dev"}
  needs: ["dev plan"]
  tags: ["dev-gp-1"]



# --- env:test

"test plan":
  <<: *plan_common
  variables: {"TF_WRK_SPC":"test"}
  tags: ["dev-gp-1"] 

"test apply":
  <<: *apply_common
  variables: {"TF_WRK_SPC":"test"}
  needs: ["test plan"] 
  tags: ["dev-gp-1"]

# --- env:stage

"stage plan":
  <<: *plan_common
  variables: {"TF_WRK_SPC":"stage"}
  tags: ["stage-gp-1"] 

"stage apply":
  <<: *apply_common
  variables: {"TF_WRK_SPC":"stage"}
  needs: ["stage plan"] 
  tags: ["stage-gp-1"]

# --- env:prod

"prod plan":
  <<: *plan_common
  variables: {"TF_WRK_SPC":"prod"}
  tags: ["prod-gp-1"] 

"prod apply":
  <<: *apply_common
  variables: {"TF_WRK_SPC":"prod"}
  needs: ["prod plan"] 
  tags: ["prod-gp-1"]

