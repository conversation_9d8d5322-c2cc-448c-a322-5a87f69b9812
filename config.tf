locals {


  aws_accounts = {
    test         = "************"
    stage        = "************"
    prod         = "************"
    team_cmp     = "************"
    ruterbastion = "************"
  }

  snowflake_accounts = {
    dev   = "fl65924"
    stage = "hp76112"
    prod  = "nn71758"
  }

  snowflake_tf_user = {
    dev   = "dev_runner_service"
    stage = "staging_runner_service"
    prod  = "prod_runner_service"
  }

  snowflake_region = {
    dev   = "eu-west-1"
    stage = "eu-west-1"
    prod  = "eu-west-1"
  }

  localuser_provider_role_fmt = "arn:aws:iam::%s:role/eks-%s-${var.team_vpc}-${var.team}-team"
  suggested_region = "eu-west-1"

  # USEFUL shortcut
  this = local.wrkspc_config[terraform.workspace]

  # -----------------------------------------------------------------------------------
  # Workspace spesific config here
  # -----------------------------------------------------------------------------------
  wrkspc_config = {
     dev = {
      region                    = local.suggested_region
      env                       = "dev"
      account_name              = "test"
      account_num               = local.aws_accounts.test
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.test, "dev")
      kube_namespace            = "tranop-dev"
      slack_channel             = "team-tran-alerts-test"
      slack_channel_assignment  = "assignment-test-alerts"

      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "FL65924"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-dev-eu-west-1-assignment-dev"
    }

    test = {
      region                    = local.suggested_region
      env                       = "test"
      account_name              = "test"
      account_num               = local.aws_accounts.test
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.test, "dev")
      kube_namespace            = "tranop"
      slack_channel             = "team-tran-alerts-test"
      slack_channel_assignment  = "assignment-test-alerts"

      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "FL65924"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-dev-eu-west-1-assignment"
    }

    stage = {
      region                    = local.suggested_region
      env                       = "stage"
      account_name              = "stage"
      account_num               = local.aws_accounts.stage
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.stage, "stage")
      kube_namespace            = "tranop"
      slack_channel             = "team-tran-alerts-stage"
      slack_channel_assignment  = "assignment-stage-alerts"

      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "HP76112"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-stage-eu-west-1-assignment"
    }

    prod = {
      region                    = local.suggested_region
      env                       = "prod"
      account_name              = "prod"
      account_num               = local.aws_accounts.prod
      localuser_assume_role_arn = format(local.localuser_provider_role_fmt, local.aws_accounts.prod, "prod")
      
      snowflake_storage_integration                = "CDP_S3_SHARED"
      snowflake_username                           = "ASSIGNMENT_SERVICE_USER"
      snowflake_account                            = "nn71758"
      snowflake_region                             = "eu-west-1"
      bucket                                       = "ruter-tranop-snowflake-prod-eu-west-1-assignment"
    }
  }

}

