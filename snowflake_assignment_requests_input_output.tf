
# ADT 3 Assignment request
resource "snowflake_table" "adt_3_assignment_request_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT3_ASSIGNMENT_REQUEST_V1_RAW"
  comment  = "Raw table of ADT3 Assingments requests from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_adt3_assignment_request" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT3_ASSIGNMENT_REQUEST_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.adt_3_assignment_request_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/mqtt.adt.ota.api.v3.input.operational.assignment.attempt.request/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_adt3_assignment_request_grant" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_adt3_assignment_request.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_adt3_assignment_request]
}

# ADT 3 Omit request mqtt.adt.ota.api.v3.input.operational.assignment.omit.request
resource "snowflake_table" "adt_3_assignment_omit_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT3_ASSIGNMENT_OMIT_REQUEST_V1_RAW"
  comment  = "Raw table of Command Assingment omit messages from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_assignment_omit_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT3_ASSIGNMENT_OMIT_V1_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.adt_3_assignment_omit_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/mqtt.adt.ota.api.v3.input.operational.assignment.omit.request/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_assignment_omit_v1" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_assignment_omit_v1.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_assignment_omit_v1]
}

# ADT 2 Assignment attempt
resource "snowflake_table" "adt2_assignment_attempt_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT2_ASSIGNMENT_ATTEMPT_V1_RAW"
  comment  = "Raw table of ADT2 Assingments attempts from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_adt2_assignment_attempt" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT2_ASSIGNMENT_REQUEST_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.adt2_assignment_attempt_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/mqtt.adt.ota.api.v2.input.di.assignment.attempt.block/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_adt2_assignment_attempt_grant" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_adt2_assignment_attempt.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_adt2_assignment_attempt]
}

# ITxPT sign-on attempt dc.event.itxpt.signon.raw
resource "snowflake_table" "adt1_signon_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT1_ASSIGNMENT_SIGNON_V1_RAW"
  comment  = "Raw table of ADT1 Assingments signon messages from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_adt1_signon_attempt" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT1_ASSIGNMENT_SIGNON_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.adt1_signon_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/dc.event.itxpt.signon.raw/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_adt1_signon_attempt" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_adt1_signon_attempt.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_adt1_signon_attempt]
}

# ITxPT sign-off attempt dc.event.itxpt.signoff.raw
resource "snowflake_table" "adt1_signoff_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT1_ASSIGNMENT_SIGNOFF_V1_RAW"
  comment  = "Raw table of ADT1 Assingments signoff messages from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_adt1_signoff_attempt" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "ADT1_ASSIGNMENT_SIGNOFF_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.adt1_signoff_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/dc.event.itxpt.signoff.raw/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_adt1_signoff_attempt" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_adt1_signoff_attempt.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_adt1_signoff_attempt]
}

# Command assignment attempt command.vehicle.assignment.assignment-attempt.v1
resource "snowflake_table" "command_assignment_attempt_v1_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "COMMAND_ASSIGNMENT_ATTEMPT_V1_RAW"
  comment  = "Raw table of Command Assingment attempt messages from kafka"
  change_tracking = true
  column {
    name     = "TIMESTAMP"
    type     = "TIMESTAMP_TZ(9)"
  }

  column {
    name     = "DATA"
    type     = "VARIANT"
    nullable = true
  }
}

resource "snowflake_pipe" "snowpipe_command_assignment_attempt_v1" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "COMMAND_ASSIGNMENT_ATTEMPT_V1_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.command_assignment_attempt_v1_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/command.vehicle.assignment.assignment-attempt.v1/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_command_assignment_attempt_v1" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_command_assignment_attempt_v1.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_command_assignment_attempt_v1]
}

resource "snowflake_table" "deviation_raw" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "DEVIATION_RAW"
  comment  = "Raw table of deviations from kafka"
  change_tracking = true
  column {
    name     = "OWNER_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "OPERATOR_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "AUTHORITY_ID"
    type     = "VARCHAR"
  }

  column {
    name     = "QUAY_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "STOPPOINT_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "DATEDJOURNEYV2_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "LINE_REFS"
    type     = "VARCHAR"
  }

  column {
    name     = "JSONDATA"
    type     = "VARIANT"
    comment  = "entire BIServiceDeviation"
  }
}

resource "snowflake_pipe" "snowpipe_deviation" {
  database = "ASSIGNMENT"
  schema   = snowflake_schema.raw_schema.name
  name     = "DEVATION_SNOWPIPE"
  comment  = "A pipe to ingest the incoming files."
  copy_statement = <<EOT
    COPY INTO ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_table.deviation_raw.name}
    FROM (
      SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
      $1 AS data
      -- TODO where do we get this
      FROM @ASSIGNMENT.${snowflake_schema.raw_schema.name}.${snowflake_stage.assignment_stage.name}/topic.name.raw/ t
    ) FILE_FORMAT = ${snowflake_schema.raw_schema.name}.json_format;
  EOT
  auto_ingest    = true
}

resource "snowflake_pipe_grant" "snowpipe_devation" {
  database_name = "ASSIGNMENT"
  schema_name   = snowflake_schema.raw_schema.name
  pipe_name     = snowflake_pipe.snowpipe_deviation.name

  privilege         = "OPERATE"
  roles             = ["ASSIGNMENT_DEVELOPER"]
  on_future         = false
  with_grant_option = false
  depends_on     = [snowflake_pipe.snowpipe_deviation]
}
