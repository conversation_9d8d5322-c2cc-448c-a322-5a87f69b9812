COPY INTO ${database_name}.${schema_name}.${table_name}
FROM (
  SELECT CONVERT_TIMEZONE( 'UTC' , current_timestamp()) AS timestamp,
  $1 AS data,
  metadata$filename AS filename,
  metadata$file_row_number AS file_row_number,
  CONCAT(metadata$filename, '_', metadata$file_row_number) AS uid
  FROM @${database_name}.${schema_name}.${stage_name}/${data_folder}/ t
) FILE_FORMAT = ${schema_name}.${file_format};
