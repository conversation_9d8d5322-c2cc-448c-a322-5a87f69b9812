

#Create an encrypted bucket and restrict access from public
resource "aws_s3_bucket" "snowflake_data_load_bucket" {
  bucket = local.this.bucket
}

resource "aws_s3_bucket_public_access_block" "snowflake_data_load_bucket_access_block" {
  bucket = aws_s3_bucket.snowflake_data_load_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

#Creating an example folder to dump some files in
resource "aws_s3_object" "topic_folder" {
  bucket = aws_s3_bucket.snowflake_data_load_bucket.bucket
  key    = "topics"
}


#Creating notifications when files are created in the example folder
#and sending to the sqs queue belonging to the corresponding snowpipe created by snowflake.
#on multiple folders, it's probably necessary to create a for_each loop or something as each of them needs 1 queue and 1 snowpipe.
resource "aws_s3_bucket_notification" "tranop_snowflake_data_load_bucket_notifications" {
  bucket = aws_s3_bucket.snowflake_data_load_bucket.bucket

  queue {
    id            = "topic-folder"
    queue_arn     = snowflake_pipe.snowpipe_assignment.notification_channel
    events        = ["s3:ObjectCreated:*"]
    filter_prefix = "topics/"
  }
  depends_on = [snowflake_pipe.snowpipe_assignment]

}



